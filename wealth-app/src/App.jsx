import { useState } from 'react'
import './styles.css'
import Dashboard from './components/Dashboard'
import GoalSetting from './components/GoalSetting'
import AssetManagement from './components/AssetManagement'
import InvestmentCockpit from './components/InvestmentCockpit'
import BehaviorIncentive from './components/BehaviorIncentive'
import Community from './components/Community'
import Navigation from './components/Navigation'
import AIAssistant from './components/AIAssistant'

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard')

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />
      case 'goals':
        return <GoalSetting />
      case 'assets':
        return <AssetManagement />
      case 'investment':
        return <InvestmentCockpit />
      case 'behavior':
        return <BehaviorIncentive />
      case 'community':
        return <Community />
      default:
        return <Dashboard />
    }
  }

  return (
    <div className="app">
      <Navigation currentPage={currentPage} setCurrentPage={setCurrentPage} />
      <main className="main-content">
        {renderPage()}
      </main>
      <AIAssistant />
    </div>
  )
}

export default App
