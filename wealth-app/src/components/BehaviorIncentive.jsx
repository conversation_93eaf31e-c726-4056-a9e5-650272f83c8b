import React, { useState } from 'react'

const BehaviorIncentive = () => {
  const [currentStreak, setCurrentStreak] = useState(12)
  const [totalPoints, setTotalPoints] = useState(2850)

  const achievements = [
    {
      id: 1,
      name: '节俭者',
      description: '连续5天自带午餐',
      icon: '🥪',
      progress: 5,
      target: 5,
      completed: true,
      reward: '理财课程券',
      points: 100
    },
    {
      id: 2,
      name: '储蓄达人',
      description: '月储蓄率超过40%',
      icon: '💰',
      progress: 3,
      target: 3,
      completed: true,
      reward: '投资分析报告',
      points: 200
    },
    {
      id: 3,
      name: '投资新手',
      description: '完成首次基金定投',
      icon: '📈',
      progress: 1,
      target: 1,
      completed: true,
      reward: '新手礼包',
      points: 150
    },
    {
      id: 4,
      name: '理财规划师',
      description: '制定并执行理财计划30天',
      icon: '📋',
      progress: 12,
      target: 30,
      completed: false,
      reward: '高级策略解锁',
      points: 500
    },
    {
      id: 5,
      name: '风险控制者',
      description: '连续3个月未超预算',
      icon: '🛡️',
      progress: 1,
      target: 3,
      completed: false,
      reward: '保险咨询服务',
      points: 300
    }
  ]

  const challenges = [
    {
      id: 1,
      title: '本周挑战：减少外卖支出',
      description: '本周外卖支出控制在300元以内',
      currentProgress: 180,
      target: 300,
      timeLeft: '3天',
      reward: '50积分 + 优惠券',
      difficulty: 'easy'
    },
    {
      id: 2,
      title: '月度挑战：储蓄率提升',
      description: '本月储蓄率达到45%',
      currentProgress: 42,
      target: 45,
      timeLeft: '8天',
      reward: '200积分 + 投资建议',
      difficulty: 'medium'
    },
    {
      id: 3,
      title: '季度挑战：投资组合优化',
      description: '夏普比率提升至1.2以上',
      currentProgress: 0.8,
      target: 1.2,
      timeLeft: '45天',
      reward: '500积分 + VIP服务',
      difficulty: 'hard'
    }
  ]

  const habits = [
    { name: '记录每日支出', streak: 12, icon: '📝', status: 'active' },
    { name: '查看投资收益', streak: 8, icon: '📊', status: 'active' },
    { name: '阅读财经新闻', streak: 5, icon: '📰', status: 'active' },
    { name: '制定周预算', streak: 3, icon: '💳', status: 'missed' }
  ]

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'var(--secondary-color)'
      case 'medium': return 'var(--warning-color)'
      case 'hard': return 'var(--danger-color)'
      default: return 'var(--text-secondary)'
    }
  }

  const getProgressPercentage = (current, target) => {
    return Math.min((current / target) * 100, 100)
  }

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      <h2 className="text-2xl font-bold mb-6">🏆 行为激励系统</h2>

      {/* 总览卡片 */}
      <div className="grid grid-3 mb-6">
        <div className="card text-center">
          <div className="text-3xl mb-2">🔥</div>
          <div className="text-2xl font-bold text-orange-600">{currentStreak}</div>
          <div className="text-sm text-gray-600">连续打卡天数</div>
        </div>
        <div className="card text-center">
          <div className="text-3xl mb-2">⭐</div>
          <div className="text-2xl font-bold text-yellow-600">{totalPoints}</div>
          <div className="text-sm text-gray-600">累计积分</div>
        </div>
        <div className="card text-center">
          <div className="text-3xl mb-2">🏅</div>
          <div className="text-2xl font-bold text-blue-600">{achievements.filter(a => a.completed).length}</div>
          <div className="text-sm text-gray-600">已获得成就</div>
        </div>
      </div>

      {/* 成就系统 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold mb-4">🏆 成就徽章</h3>
        <div className="grid grid-2">
          {achievements.map(achievement => (
            <div 
              key={achievement.id} 
              className={`p-4 rounded-lg border-2 ${
                achievement.completed 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-start space-x-3">
                <div className={`text-3xl ${achievement.completed ? '' : 'grayscale opacity-50'}`}>
                  {achievement.icon}
                </div>
                <div className="flex-1">
                  <div className="font-semibold mb-1">{achievement.name}</div>
                  <div className="text-sm text-gray-600 mb-2">{achievement.description}</div>
                  
                  <div className="mb-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span>进度</span>
                      <span>{achievement.progress}/{achievement.target}</span>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ 
                          width: `${getProgressPercentage(achievement.progress, achievement.target)}%`,
                          background: achievement.completed ? 'var(--secondary-color)' : 'var(--text-secondary)'
                        }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      奖励: {achievement.reward}
                    </div>
                    <div className="text-sm font-bold text-blue-600">
                      +{achievement.points}分
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 挑战任务 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold mb-4">🎯 挑战任务</h3>
        <div className="space-y-4">
          {challenges.map(challenge => (
            <div key={challenge.id} className="p-4 border rounded-lg">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div className="font-semibold">{challenge.title}</div>
                  <div className="text-sm text-gray-600">{challenge.description}</div>
                </div>
                <div className="text-right">
                  <div 
                    className="px-2 py-1 rounded text-xs font-medium"
                    style={{ 
                      backgroundColor: getDifficultyColor(challenge.difficulty) + '20',
                      color: getDifficultyColor(challenge.difficulty)
                    }}
                  >
                    {challenge.difficulty === 'easy' ? '简单' : challenge.difficulty === 'medium' ? '中等' : '困难'}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">剩余: {challenge.timeLeft}</div>
                </div>
              </div>
              
              <div className="mb-3">
                <div className="flex justify-between text-sm mb-1">
                  <span>当前进度</span>
                  <span>
                    {typeof challenge.currentProgress === 'number' && challenge.currentProgress < 10 
                      ? challenge.currentProgress.toFixed(1) 
                      : challenge.currentProgress}
                    /{challenge.target}
                    {challenge.title.includes('外卖') ? '元' : challenge.title.includes('储蓄率') ? '%' : ''}
                  </span>
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ 
                      width: `${getProgressPercentage(challenge.currentProgress, challenge.target)}%`,
                      background: getDifficultyColor(challenge.difficulty)
                    }}
                  ></div>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  奖励: {challenge.reward}
                </div>
                <button className="btn btn-primary btn-sm">
                  查看详情
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 习惯追踪 */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">📅 习惯追踪</h3>
        <div className="grid grid-2">
          {habits.map((habit, index) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-xl">{habit.icon}</span>
                  <span className="font-medium">{habit.name}</span>
                </div>
                <div className={`w-3 h-3 rounded-full ${
                  habit.status === 'active' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
              </div>
              <div className="text-sm text-gray-600">
                连续 {habit.streak} 天
                {habit.status === 'missed' && (
                  <span className="text-red-500 ml-2">今日未完成</span>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="font-medium text-blue-800 mb-2">💡 习惯建议</div>
          <div className="text-sm text-blue-600">
            坚持记录支出习惯很棒！建议添加"每周复盘"习惯，提升理财效果。
          </div>
        </div>
      </div>

      {/* 损失厌恶机制 */}
      <div className="card mt-6">
        <h3 className="text-lg font-semibold mb-4">⚠️ 损失厌恶机制</h3>
        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
          <div className="font-medium text-red-800 mb-2">🚨 超预算惩罚设置</div>
          <div className="text-sm text-red-600 mb-3">
            当月支出超预算时，超出金额将自动投入高波动基金（可能产生亏损）
          </div>
          <div className="flex items-center justify-between">
            <div className="text-sm">
              本月超预算: <span className="font-bold">¥0</span> (未触发)
            </div>
            <button className="btn btn-secondary btn-sm">
              调整设置
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BehaviorIncentive
