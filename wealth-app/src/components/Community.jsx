import React, { useState } from 'react'

const Community = () => {
  const [activeTab, setActiveTab] = useState('feed')

  const posts = [
    {
      id: 1,
      author: '理财小白',
      avatar: '👤',
      time: '2小时前',
      content: '刚完成了第一个月的定投，虽然只有1000元，但感觉很有成就感！大家都是怎么开始的？',
      likes: 23,
      comments: 8,
      tags: ['新手', '定投']
    },
    {
      id: 2,
      author: '投资达人',
      avatar: '💼',
      time: '5小时前',
      content: '分享一个小技巧：每次发工资后立即转走储蓄金额，剩下的才是可花费的。这样能有效避免月光。',
      likes: 156,
      comments: 34,
      tags: ['技巧', '储蓄']
    },
    {
      id: 3,
      author: '基金研究员',
      avatar: '📊',
      time: '1天前',
      content: '最近市场波动较大，建议大家保持定投节奏，不要因为短期波动而改变长期策略。历史数据显示...',
      likes: 89,
      comments: 21,
      tags: ['市场分析', '定投']
    }
  ]

  const groups = [
    {
      id: 1,
      name: '新手理财交流',
      members: 1234,
      description: '理财新手互助学习，分享基础知识',
      category: '学习',
      isJoined: true
    },
    {
      id: 2,
      name: '基金定投俱乐部',
      members: 2567,
      description: '专注基金定投策略讨论',
      category: '投资',
      isJoined: true
    },
    {
      id: 3,
      name: '节约生活分享',
      members: 892,
      description: '分享省钱技巧和生活智慧',
      category: '生活',
      isJoined: false
    },
    {
      id: 4,
      name: '财务自由之路',
      members: 3456,
      description: '讨论财务自由的实现路径',
      category: '目标',
      isJoined: false
    }
  ]

  const experts = [
    {
      id: 1,
      name: '张理财师',
      title: 'CFP持证理财师',
      avatar: '👨‍💼',
      followers: 12500,
      expertise: ['资产配置', '保险规划'],
      isFollowed: true
    },
    {
      id: 2,
      name: '李基金经理',
      title: '10年基金投资经验',
      avatar: '👩‍💼',
      followers: 8900,
      expertise: ['基金选择', '市场分析'],
      isFollowed: false
    },
    {
      id: 3,
      name: '王财务顾问',
      title: '企业财务规划专家',
      avatar: '🧑‍💼',
      followers: 6700,
      expertise: ['税务筹划', '企业理财'],
      isFollowed: false
    }
  ]

  const events = [
    {
      id: 1,
      title: '基金定投策略分享会',
      time: '2024-01-15 19:00',
      speaker: '李基金经理',
      participants: 156,
      type: 'online'
    },
    {
      id: 2,
      title: '新手理财入门课程',
      time: '2024-01-18 20:00',
      speaker: '张理财师',
      participants: 89,
      type: 'online'
    },
    {
      id: 3,
      title: '年度理财规划工作坊',
      time: '2024-01-20 14:00',
      speaker: '王财务顾问',
      participants: 45,
      type: 'offline'
    }
  ]

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      <h2 className="text-2xl font-bold mb-6">👥 理财社群</h2>

      {/* 标签页导航 */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'feed', label: '动态', icon: '📱' },
            { id: 'groups', label: '小组', icon: '👥' },
            { id: 'experts', label: '专家', icon: '🎓' },
            { id: 'events', label: '活动', icon: '📅' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
                activeTab === tab.id 
                  ? 'bg-white text-blue-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {tab.icon} {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* 动态流 */}
      {activeTab === 'feed' && (
        <div className="space-y-6">
          <div className="card">
            <div className="p-4 border-b">
              <textarea 
                placeholder="分享你的理财心得..."
                className="w-full p-3 border rounded-lg resize-none"
                rows="3"
              />
              <div className="flex justify-between items-center mt-3">
                <div className="flex space-x-2">
                  <button className="text-blue-600 hover:text-blue-800 text-sm">📷 图片</button>
                  <button className="text-blue-600 hover:text-blue-800 text-sm">📊 数据</button>
                  <button className="text-blue-600 hover:text-blue-800 text-sm"># 话题</button>
                </div>
                <button className="btn btn-primary">发布</button>
              </div>
            </div>
          </div>

          {posts.map(post => (
            <div key={post.id} className="card">
              <div className="flex items-start space-x-3 mb-3">
                <div className="text-2xl">{post.avatar}</div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-semibold">{post.author}</span>
                    <span className="text-sm text-gray-500">{post.time}</span>
                  </div>
                  <div className="text-gray-800 mb-3">{post.content}</div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {post.tags.map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        #{tag}
                      </span>
                    ))}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <button className="flex items-center space-x-1 hover:text-red-500">
                      <span>❤️</span>
                      <span>{post.likes}</span>
                    </button>
                    <button className="flex items-center space-x-1 hover:text-blue-500">
                      <span>💬</span>
                      <span>{post.comments}</span>
                    </button>
                    <button className="hover:text-green-500">🔗 分享</button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 小组 */}
      {activeTab === 'groups' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">🔍 发现小组</h3>
            <div className="grid grid-2">
              {groups.map(group => (
                <div key={group.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-semibold">{group.name}</div>
                      <div className="text-sm text-gray-600">{group.members} 成员</div>
                    </div>
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      {group.category}
                    </span>
                  </div>
                  <div className="text-sm text-gray-700 mb-3">{group.description}</div>
                  <button className={`btn w-full ${
                    group.isJoined ? 'btn-secondary' : 'btn-primary'
                  }`}>
                    {group.isJoined ? '已加入' : '加入小组'}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 专家 */}
      {activeTab === 'experts' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">🎓 理财专家</h3>
            <div className="space-y-4">
              {experts.map(expert => (
                <div key={expert.id} className="p-4 border rounded-lg">
                  <div className="flex items-start space-x-4">
                    <div className="text-3xl">{expert.avatar}</div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-semibold">{expert.name}</div>
                          <div className="text-sm text-gray-600">{expert.title}</div>
                          <div className="text-sm text-gray-500">{expert.followers} 关注者</div>
                        </div>
                        <button className={`btn ${
                          expert.isFollowed ? 'btn-secondary' : 'btn-primary'
                        }`}>
                          {expert.isFollowed ? '已关注' : '关注'}
                        </button>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {expert.expertise.map((skill, index) => (
                          <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 活动 */}
      {activeTab === 'events' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">📅 近期活动</h3>
            <div className="space-y-4">
              {events.map(event => (
                <div key={event.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-semibold">{event.title}</div>
                      <div className="text-sm text-gray-600">主讲: {event.speaker}</div>
                      <div className="text-sm text-gray-500">时间: {event.time}</div>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 text-xs rounded ${
                        event.type === 'online' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {event.type === 'online' ? '线上' : '线下'}
                      </span>
                      <div className="text-sm text-gray-500 mt-1">
                        {event.participants} 人参与
                      </div>
                    </div>
                  </div>
                  <button className="btn btn-primary w-full">
                    报名参加
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 用户自由操作空间提示 */}
      <div className="card mt-6">
        <h3 className="text-lg font-semibold mb-4">🎮 自由操作空间</h3>
        <div className="grid grid-3">
          <div className="p-3 bg-blue-50 rounded-lg text-center">
            <div className="text-2xl mb-2">📝</div>
            <div className="font-medium">创建小组</div>
            <div className="text-sm text-gray-600">发起自己的理财话题</div>
          </div>
          <div className="p-3 bg-green-50 rounded-lg text-center">
            <div className="text-2xl mb-2">🎯</div>
            <div className="font-medium">发起挑战</div>
            <div className="text-sm text-gray-600">邀请朋友一起理财</div>
          </div>
          <div className="p-3 bg-purple-50 rounded-lg text-center">
            <div className="text-2xl mb-2">📊</div>
            <div className="font-medium">分享成果</div>
            <div className="text-sm text-gray-600">展示你的理财成就</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Community
