import React from 'react'

const Dashboard = () => {
  // 模拟数据
  const goalData = {
    target: 2000000,
    current: 420000,
    timeline: 24,
    elapsed: 5
  }

  const progress = (goalData.current / goalData.target) * 100

  const assets = [
    { name: '现金及活期', amount: 85000, percentage: 20.2, risk: 'low' },
    { name: '定期存款', amount: 120000, percentage: 28.6, risk: 'low' },
    { name: '股票基金', amount: 150000, percentage: 35.7, risk: 'high' },
    { name: '债券基金', amount: 65000, percentage: 15.5, risk: 'medium' }
  ]

  const monthlyMetrics = {
    income: 28000,
    expense: 16800,
    saving: 11200,
    savingRate: 40
  }

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'low': return 'var(--secondary-color)'
      case 'medium': return 'var(--warning-color)'
      case 'high': return 'var(--danger-color)'
      default: return 'var(--text-secondary)'
    }
  }

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      {/* 目标进度卡片 */}
      <div className="goal-progress">
        <h2 className="text-2xl font-bold mb-4">
          🎯 2年200万目标进度
        </h2>
        <div className="grid grid-2" style={{ alignItems: 'center' }}>
          <div>
            <div className="text-3xl font-bold mb-2">
              ¥{goalData.current.toLocaleString()} / ¥{goalData.target.toLocaleString()}
            </div>
            <div className="progress-bar mb-4">
              <div 
                className="progress-fill" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="text-lg">
              进度: {progress.toFixed(1)}% | 剩余时间: {goalData.timeline - goalData.elapsed}个月
            </div>
          </div>
          <div className="text-center">
            <div className="text-xl mb-2">当前状态</div>
            <div className="text-3xl font-bold">
              {progress < 20 ? '🔥 需要加速' : progress < 50 ? '⚡ 稳步推进' : '🚀 超预期'}
            </div>
          </div>
        </div>
      </div>

      {/* 核心指标 */}
      <div className="grid grid-4 mb-6">
        <div className="card metric-card">
          <div className="metric-value text-success">¥{monthlyMetrics.income.toLocaleString()}</div>
          <div className="metric-label">月收入</div>
        </div>
        <div className="card metric-card">
          <div className="metric-value text-warning">¥{monthlyMetrics.expense.toLocaleString()}</div>
          <div className="metric-label">月支出</div>
        </div>
        <div className="card metric-card">
          <div className="metric-value text-primary">¥{monthlyMetrics.saving.toLocaleString()}</div>
          <div className="metric-label">月储蓄</div>
        </div>
        <div className="card metric-card">
          <div className="metric-value text-secondary">{monthlyMetrics.savingRate}%</div>
          <div className="metric-label">储蓄率</div>
        </div>
      </div>

      <div className="grid grid-2">
        {/* 资产分布 */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">💼 资产分布热力图</h3>
          <div className="space-y-3">
            {assets.map((asset, index) => (
              <div key={index} className="asset-item">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{asset.name}</span>
                  <span className="text-lg font-bold">¥{asset.amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="progress-bar flex-1">
                    <div 
                      className="progress-fill" 
                      style={{ 
                        width: `${asset.percentage}%`,
                        background: getRiskColor(asset.risk)
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-secondary">{asset.percentage}%</span>
                  <span 
                    className="status-indicator"
                    style={{ backgroundColor: getRiskColor(asset.risk) }}
                  ></span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-sm text-secondary">
            🔴 高风险 🟡 中风险 🟢 低风险
          </div>
        </div>

        {/* AI洞察 */}
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">🤖 AI智能洞察</h3>
          <div className="space-y-4">
            <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <div className="font-medium text-blue-800">💡 储蓄优化建议</div>
              <div className="text-sm text-blue-600 mt-1">
                检测到您的外卖支出本月达到4,780元，压缩空间约80%，可增加月储蓄3,800元
              </div>
            </div>
            <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
              <div className="font-medium text-green-800">📈 投资机会</div>
              <div className="text-sm text-green-600 mt-1">
                当前沪深300 PE为11.8，低于历史均值，建议增加股票基金定投金额
              </div>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
              <div className="font-medium text-yellow-800">⚠️ 风险提醒</div>
              <div className="text-sm text-yellow-600 mt-1">
                您的基金组合夏普比率仅0.8，低于同类平均水平，建议优化配置
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="card mt-6">
        <h3 className="text-xl font-semibold mb-4">⚡ 快速操作</h3>
        <div className="grid grid-4">
          <button className="btn btn-primary">
            💰 一键定投
          </button>
          <button className="btn btn-secondary">
            📊 资产再平衡
          </button>
          <button className="btn btn-success">
            🎯 调整目标
          </button>
          <button className="btn btn-warning">
            📱 连接账户
          </button>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
