import React, { useState } from 'react'

const GoalSetting = () => {
  const [goalForm, setGoalForm] = useState({
    target: 2000000,
    timeline: 24,
    currentAssets: 420000,
    monthlyIncome: 28000,
    riskTolerance: 'medium'
  })

  const [simulation, setSimulation] = useState(null)

  const handleInputChange = (field, value) => {
    setGoalForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const runSimulation = () => {
    const { target, timeline, currentAssets, monthlyIncome } = goalForm
    const remainingAmount = target - currentAssets
    const monthsRemaining = timeline
    const requiredMonthlySaving = remainingAmount / monthsRemaining
    const currentSavingCapacity = monthlyIncome * 0.4 // 假设40%储蓄率
    
    const scenarios = [
      {
        name: '保守方案',
        requiredReturn: 0.03,
        monthlySaving: Math.min(requiredMonthlySaving, currentSavingCapacity),
        feasibility: requiredMonthlySaving <= currentSavingCapacity ? 'high' : 'low',
        description: '3%年化收益，稳健投资'
      },
      {
        name: '平衡方案',
        requiredReturn: 0.08,
        monthlySaving: requiredMonthlySaving * 0.8,
        feasibility: 'medium',
        description: '8%年化收益，股债平衡'
      },
      {
        name: '激进方案',
        requiredReturn: 0.15,
        monthlySaving: requiredMonthlySaving * 0.6,
        feasibility: 'high',
        description: '15%年化收益，高风险高收益'
      }
    ]

    setSimulation({
      requiredMonthlySaving,
      currentCapacity: currentSavingCapacity,
      scenarios,
      recommendation: requiredMonthlySaving > currentSavingCapacity * 1.5 ? 'adjust' : 'achievable'
    })
  }

  const getFeasibilityColor = (feasibility) => {
    switch (feasibility) {
      case 'high': return 'var(--secondary-color)'
      case 'medium': return 'var(--warning-color)'
      case 'low': return 'var(--danger-color)'
      default: return 'var(--text-secondary)'
    }
  }

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      <div className="card mb-6">
        <h2 className="text-2xl font-bold mb-6">🎯 智能目标推演引擎</h2>
        
        <div className="grid grid-2 mb-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">目标设定</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">目标金额 (万元)</label>
                <input
                  type="number"
                  value={goalForm.target / 10000}
                  onChange={(e) => handleInputChange('target', e.target.value * 10000)}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">时间期限 (月)</label>
                <input
                  type="number"
                  value={goalForm.timeline}
                  onChange={(e) => handleInputChange('timeline', parseInt(e.target.value))}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">当前资产 (万元)</label>
                <input
                  type="number"
                  value={goalForm.currentAssets / 10000}
                  onChange={(e) => handleInputChange('currentAssets', e.target.value * 10000)}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">月收入 (元)</label>
                <input
                  type="number"
                  value={goalForm.monthlyIncome}
                  onChange={(e) => handleInputChange('monthlyIncome', parseInt(e.target.value))}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">风险偏好</h3>
            <div className="space-y-3">
              {[
                { value: 'conservative', label: '保守型', desc: '优先保本，接受较低收益' },
                { value: 'medium', label: '平衡型', desc: '追求稳健收益，适度承担风险' },
                { value: 'aggressive', label: '激进型', desc: '追求高收益，愿意承担高风险' }
              ].map(option => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="riskTolerance"
                    value={option.value}
                    checked={goalForm.riskTolerance === option.value}
                    onChange={(e) => handleInputChange('riskTolerance', e.target.value)}
                    className="mt-1"
                  />
                  <div>
                    <div className="font-medium">{option.label}</div>
                    <div className="text-sm text-secondary">{option.desc}</div>
                  </div>
                </label>
              ))}
            </div>
            
            <button 
              onClick={runSimulation}
              className="btn btn-primary w-full mt-6"
            >
              🔮 开始AI推演
            </button>
          </div>
        </div>
      </div>

      {simulation && (
        <div className="card">
          <h3 className="text-xl font-semibold mb-4">📊 推演结果</h3>
          
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-800 mb-2">
              💰 需要月储蓄: ¥{simulation.requiredMonthlySaving.toLocaleString()}
            </div>
            <div className="text-sm text-blue-600">
              当前储蓄能力: ¥{simulation.currentCapacity.toLocaleString()} 
              {simulation.requiredMonthlySaving > simulation.currentCapacity && 
                <span className="text-red-600 ml-2">⚠️ 超出当前能力</span>
              }
            </div>
          </div>

          <div className="grid grid-3 mb-6">
            {simulation.scenarios.map((scenario, index) => (
              <div key={index} className="card" style={{ border: `2px solid ${getFeasibilityColor(scenario.feasibility)}` }}>
                <h4 className="font-semibold mb-3">{scenario.name}</h4>
                <div className="space-y-2 text-sm">
                  <div>年化收益: <span className="font-bold">{(scenario.requiredReturn * 100).toFixed(1)}%</span></div>
                  <div>月储蓄: <span className="font-bold">¥{scenario.monthlySaving.toLocaleString()}</span></div>
                  <div>可行性: 
                    <span 
                      className="font-bold ml-1"
                      style={{ color: getFeasibilityColor(scenario.feasibility) }}
                    >
                      {scenario.feasibility === 'high' ? '高' : scenario.feasibility === 'medium' ? '中' : '低'}
                    </span>
                  </div>
                  <div className="text-xs text-secondary mt-2">{scenario.description}</div>
                </div>
              </div>
            ))}
          </div>

          {simulation.recommendation === 'adjust' && (
            <div className="p-4 bg-red-50 rounded-lg border-l-4 border-red-500">
              <h4 className="font-semibold text-red-800 mb-2">⚠️ 目标调整建议</h4>
              <div className="text-sm text-red-600 space-y-1">
                <div>• 当前目标过于激进，建议调整以下任一条件：</div>
                <div>• 延长时间至 {Math.ceil(goalForm.timeline * 1.5)} 个月</div>
                <div>• 降低目标至 ¥{(goalForm.target * 0.7).toLocaleString()}</div>
                <div>• 增加月收入至 ¥{(simulation.requiredMonthlySaving / 0.4).toLocaleString()}</div>
              </div>
            </div>
          )}

          <div className="mt-6 grid grid-2">
            <button className="btn btn-success">
              ✅ 采用推荐方案
            </button>
            <button className="btn btn-secondary">
              🔄 重新推演
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default GoalSetting
