import React, { useState } from 'react'

const AssetManagement = () => {
  const [activeTab, setActiveTab] = useState('overview')

  const assets = [
    { id: 1, name: '招商银行储蓄卡', type: 'cash', amount: 85000, yield: 0.3, risk: 'low' },
    { id: 2, name: '建设银行定期', type: 'deposit', amount: 120000, yield: 2.8, risk: 'low' },
    { id: 3, name: '易方达沪深300ETF', type: 'fund', amount: 150000, yield: 12.5, risk: 'high' },
    { id: 4, name: '华夏债券基金', type: 'bond', amount: 65000, yield: 4.2, risk: 'medium' }
  ]

  const expenses = [
    { category: '房租', amount: 4800, percentage: 28.6, trend: 'stable' },
    { category: '外卖', amount: 4780, percentage: 28.5, trend: 'up', alert: true },
    { category: '交通', amount: 1200, percentage: 7.1, trend: 'down' },
    { category: '娱乐', amount: 2800, percentage: 16.7, trend: 'up' },
    { category: '购物', amount: 2200, percentage: 13.1, trend: 'stable' },
    { category: '其他', amount: 1020, percentage: 6.1, trend: 'stable' }
  ]

  const debts = [
    { name: '信用卡账单', amount: 8500, rate: 18.0, minPayment: 850, type: 'credit' },
    { name: '花呗', amount: 3200, rate: 15.0, minPayment: 320, type: 'consumer' }
  ]

  const getAssetIcon = (type) => {
    switch (type) {
      case 'cash': return '💰'
      case 'deposit': return '🏦'
      case 'fund': return '📈'
      case 'bond': return '📊'
      default: return '💼'
    }
  }

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'low': return 'var(--secondary-color)'
      case 'medium': return 'var(--warning-color)'
      case 'high': return 'var(--danger-color)'
      default: return 'var(--text-secondary)'
    }
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return '📈'
      case 'down': return '📉'
      case 'stable': return '➡️'
      default: return '➡️'
    }
  }

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      <h2 className="text-2xl font-bold mb-6">💼 动态资产负债表</h2>

      {/* 标签页导航 */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'overview', label: '资产概览', icon: '📊' },
            { id: 'cashflow', label: '现金流手术刀', icon: '🔍' },
            { id: 'debt', label: '债务灭火器', icon: '🔥' },
            { id: 'diagnosis', label: '资产诊断仪', icon: '🩺' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
                activeTab === tab.id 
                  ? 'bg-white text-blue-600 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {tab.icon} {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* 资产概览 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div className="grid grid-2">
            <div className="card">
              <h3 className="text-lg font-semibold mb-4">💰 资产明细</h3>
              <div className="space-y-3">
                {assets.map(asset => (
                  <div key={asset.id} className="asset-card card p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-xl">{getAssetIcon(asset.type)}</span>
                        <div>
                          <div className="font-medium">{asset.name}</div>
                          <div className="text-sm text-secondary">
                            年化收益: {asset.yield}%
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">¥{asset.amount.toLocaleString()}</div>
                        <div 
                          className="text-xs px-2 py-1 rounded"
                          style={{ 
                            backgroundColor: getRiskColor(asset.risk) + '20',
                            color: getRiskColor(asset.risk)
                          }}
                        >
                          {asset.risk === 'low' ? '低风险' : asset.risk === 'medium' ? '中风险' : '高风险'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="font-semibold text-blue-800">
                  总资产: ¥{assets.reduce((sum, asset) => sum + asset.amount, 0).toLocaleString()}
                </div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold mb-4">📈 资产配置建议</h3>
              <div className="space-y-4">
                <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                  <div className="font-medium text-green-800">✅ 优化建议</div>
                  <div className="text-sm text-green-600 mt-1">
                    现金比例偏高(20.2%)，建议转入短债基金提升收益
                  </div>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                  <div className="font-medium text-yellow-800">⚠️ 风险提醒</div>
                  <div className="text-sm text-yellow-600 mt-1">
                    股票基金占比35.7%，建议根据市场情况适当调整
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                  <div className="font-medium text-blue-800">💡 机会提醒</div>
                  <div className="text-sm text-blue-600 mt-1">
                    检测到微信零钱5.2万，建议转入货币基金
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 现金流分析 */}
      {activeTab === 'cashflow' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">🔍 支出分析 - AI识别消费黑洞</h3>
            <div className="grid grid-2">
              <div>
                <h4 className="font-medium mb-3">本月支出明细</h4>
                <div className="space-y-3">
                  {expenses.map((expense, index) => (
                    <div key={index} className={`p-3 rounded-lg ${expense.alert ? 'bg-red-50 border border-red-200' : 'bg-gray-50'}`}>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <span>{getTrendIcon(expense.trend)}</span>
                          <span className="font-medium">{expense.category}</span>
                          {expense.alert && <span className="text-red-500 text-xs">⚠️ 异常</span>}
                        </div>
                        <div className="text-right">
                          <div className="font-bold">¥{expense.amount.toLocaleString()}</div>
                          <div className="text-sm text-secondary">{expense.percentage}%</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-3">AI优化建议</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                    <div className="font-medium text-red-800">🚨 消费黑洞发现</div>
                    <div className="text-sm text-red-600 mt-1">
                      外卖支出4,780元，占比28.5%，建议：
                      <ul className="mt-2 space-y-1">
                        <li>• 每周自带午餐3天，可节省1,500元</li>
                        <li>• 使用优惠券和会员折扣</li>
                        <li>• 设置月度外卖预算上限3,000元</li>
                      </ul>
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                    <div className="font-medium text-green-800">💰 节省潜力</div>
                    <div className="text-sm text-green-600 mt-1">
                      优化后月节省: ¥2,280<br/>
                      年度额外储蓄: ¥27,360
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 债务管理 */}
      {activeTab === 'debt' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">🔥 债务灭火器 - 高息负债优先还款</h3>
            <div className="space-y-4">
              {debts.map((debt, index) => (
                <div key={index} className="p-4 border border-red-200 rounded-lg bg-red-50">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-semibold text-red-800">{debt.name}</div>
                      <div className="text-sm text-red-600">年利率: {debt.rate}%</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-red-800">¥{debt.amount.toLocaleString()}</div>
                      <div className="text-sm text-red-600">最低还款: ¥{debt.minPayment}</div>
                    </div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="text-sm">
                      <div className="font-medium mb-2">还款策略建议:</div>
                      <div className="space-y-1 text-gray-600">
                        <div>• 提前全额还清可节省年利息: ¥{(debt.amount * debt.rate / 100).toLocaleString()}</div>
                        <div>• 建议优先级: {debt.rate >= 15 ? '🔥 极高' : '⚡ 高'}</div>
                        <div>• 预计还清时间: {Math.ceil(debt.amount / (debt.minPayment * 2))}个月</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="font-semibold text-blue-800 mb-2">💡 债务优化方案</div>
              <div className="text-sm text-blue-600">
                总债务: ¥{debts.reduce((sum, debt) => sum + debt.amount, 0).toLocaleString()}<br/>
                年利息支出: ¥{debts.reduce((sum, debt) => sum + (debt.amount * debt.rate / 100), 0).toLocaleString()}<br/>
                建议6个月内全部清偿，可节省利息¥{(debts.reduce((sum, debt) => sum + (debt.amount * debt.rate / 100), 0) * 0.5).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 资产诊断 */}
      {activeTab === 'diagnosis' && (
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-4">🩺 资产诊断仪 - 收益风险评估</h3>
            <div className="grid grid-2">
              <div>
                <h4 className="font-medium mb-3">投资组合分析</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium mb-2">夏普比率</div>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold text-orange-600">0.8</div>
                      <div className="text-sm text-gray-600">
                        <div>同类平均: 1.2</div>
                        <div className="text-orange-600">⚠️ 低于平均</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium mb-2">最大回撤</div>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold text-red-600">-15.2%</div>
                      <div className="text-sm text-gray-600">
                        <div>风险等级: 中高</div>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="font-medium mb-2">年化收益</div>
                    <div className="flex items-center space-x-2">
                      <div className="text-2xl font-bold text-green-600">8.5%</div>
                      <div className="text-sm text-gray-600">
                        <div>超越通胀: +6.0%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-3">优化建议</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                    <div className="font-medium text-blue-800">📈 配置优化</div>
                    <div className="text-sm text-blue-600 mt-1">
                      建议增加债券基金至30%，降低组合波动性
                    </div>
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                    <div className="font-medium text-green-800">🎯 再平衡</div>
                    <div className="text-sm text-green-600 mt-1">
                      股债比例偏离目标5%，建议执行再平衡
                    </div>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                    <div className="font-medium text-yellow-800">⏰ 定期检查</div>
                    <div className="text-sm text-yellow-600 mt-1">
                      建议每季度重新评估资产配置
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AssetManagement
