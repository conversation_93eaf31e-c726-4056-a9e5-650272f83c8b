import React from 'react'

const Navigation = ({ currentPage, setCurrentPage }) => {
  const navItems = [
    { id: 'dashboard', label: '仪表板', icon: '📊' },
    { id: 'goals', label: '目标设定', icon: '🎯' },
    { id: 'assets', label: '资产管理', icon: '💰' },
    { id: 'investment', label: '投资驾驶舱', icon: '📈' },
    { id: 'behavior', label: '行为激励', icon: '🏆' },
    { id: 'community', label: '社群', icon: '👥' }
  ]

  return (
    <nav className="nav">
      <div className="container">
        <div className="nav-container">
          <a href="#" className="nav-brand">
            💎 启航财富引擎
          </a>
          <ul className="nav-menu">
            {navItems.map(item => (
              <li key={item.id}>
                <a
                  href="#"
                  className={`nav-link ${currentPage === item.id ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault()
                    setCurrentPage(item.id)
                  }}
                >
                  <span style={{ marginRight: '0.5rem' }}>{item.icon}</span>
                  {item.label}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </nav>
  )
}

export default Navigation
