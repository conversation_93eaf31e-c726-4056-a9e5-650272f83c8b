import React, { useState } from 'react'

const InvestmentCockpit = () => {
  const [autoInvestEnabled, setAutoInvestEnabled] = useState(true)
  const [rebalanceThreshold, setRebalanceThreshold] = useState(5)

  const portfolio = {
    target: { stocks: 70, bonds: 30 },
    current: { stocks: 75, bonds: 25 },
    deviation: 5
  }

  const investments = [
    {
      name: '易方达沪深300ETF',
      code: '510310',
      type: 'stock',
      allocation: 40,
      currentValue: 150000,
      monthlyInvest: 3000,
      performance: 12.5,
      status: 'active'
    },
    {
      name: '华夏债券基金',
      code: '001001',
      type: 'bond',
      allocation: 25,
      currentValue: 65000,
      monthlyInvest: 1500,
      performance: 4.2,
      status: 'active'
    },
    {
      name: '南方中证500ETF',
      code: '510500',
      type: 'stock',
      allocation: 30,
      currentValue: 85000,
      monthlyInvest: 2000,
      performance: 8.7,
      status: 'paused'
    }
  ]

  const marketSignals = [
    {
      indicator: '沪深300 PE',
      value: 11.8,
      benchmark: 13.5,
      signal: 'buy',
      description: '低于历史均值，建议加仓'
    },
    {
      indicator: '十年期国债收益率',
      value: 2.8,
      benchmark: 3.2,
      signal: 'hold',
      description: '处于合理区间'
    },
    {
      indicator: 'VIX恐慌指数',
      value: 18.5,
      benchmark: 20.0,
      signal: 'neutral',
      description: '市场情绪稳定'
    }
  ]

  const getSignalColor = (signal) => {
    switch (signal) {
      case 'buy': return 'var(--secondary-color)'
      case 'sell': return 'var(--danger-color)'
      case 'hold': return 'var(--warning-color)'
      default: return 'var(--text-secondary)'
    }
  }

  const getSignalIcon = (signal) => {
    switch (signal) {
      case 'buy': return '📈'
      case 'sell': return '📉'
      case 'hold': return '⏸️'
      default: return '➡️'
    }
  }

  return (
    <div className="container" style={{ paddingTop: '2rem' }}>
      <h2 className="text-2xl font-bold mb-6">🚀 AI投资驾驶舱</h2>

      {/* 自动投资控制面板 */}
      <div className="card mb-6">
        <h3 className="text-lg font-semibold mb-4">⚙️ 自动投资设置</h3>
        <div className="grid grid-3">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">自动定投</span>
              <label className="switch">
                <input
                  type="checkbox"
                  checked={autoInvestEnabled}
                  onChange={(e) => setAutoInvestEnabled(e.target.checked)}
                />
                <span className="slider"></span>
              </label>
            </div>
            <div className="text-sm text-gray-600">
              {autoInvestEnabled ? '✅ 已启用' : '❌ 已暂停'}
            </div>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="font-medium mb-2">再平衡阈值</div>
            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="3"
                max="10"
                value={rebalanceThreshold}
                onChange={(e) => setRebalanceThreshold(e.target.value)}
                className="flex-1"
              />
              <span className="text-sm font-bold">{rebalanceThreshold}%</span>
            </div>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <div className="font-medium mb-2">当前偏离度</div>
            <div className="text-2xl font-bold text-orange-600">{portfolio.deviation}%</div>
            <div className="text-sm text-gray-600">
              {portfolio.deviation >= rebalanceThreshold ? '🚨 需要再平衡' : '✅ 正常范围'}
            </div>
          </div>
        </div>
      </div>

      {/* 投资组合状态 */}
      <div className="grid grid-2 mb-6">
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">📊 投资组合配置</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span>股票基金</span>
                <span>{portfolio.current.stocks}% (目标: {portfolio.target.stocks}%)</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ 
                    width: `${portfolio.current.stocks}%`,
                    background: portfolio.current.stocks > portfolio.target.stocks ? 'var(--warning-color)' : 'var(--secondary-color)'
                  }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-2">
                <span>债券基金</span>
                <span>{portfolio.current.bonds}% (目标: {portfolio.target.bonds}%)</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ 
                    width: `${portfolio.current.bonds}%`,
                    background: portfolio.current.bonds < portfolio.target.bonds ? 'var(--warning-color)' : 'var(--primary-color)'
                  }}
                ></div>
              </div>
            </div>
          </div>
          
          {portfolio.deviation >= rebalanceThreshold && (
            <div className="mt-4 p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
              <div className="font-medium text-orange-800">🔄 再平衡建议</div>
              <div className="text-sm text-orange-600 mt-1">
                股票基金超配5%，建议卖出¥{((portfolio.current.stocks - portfolio.target.stocks) * 3000).toLocaleString()}转入债券基金
              </div>
              <button className="btn btn-warning mt-2">
                一键再平衡
              </button>
            </div>
          )}
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold mb-4">📈 市场信号监控</h3>
          <div className="space-y-3">
            {marketSignals.map((signal, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">{signal.indicator}</div>
                  <div className="flex items-center space-x-1">
                    <span>{getSignalIcon(signal.signal)}</span>
                    <span 
                      className="text-sm font-bold"
                      style={{ color: getSignalColor(signal.signal) }}
                    >
                      {signal.signal.toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  当前: {signal.value} | 基准: {signal.benchmark}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {signal.description}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 定投明细 */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">💰 定投明细管理</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3">基金名称</th>
                <th className="text-left py-3">代码</th>
                <th className="text-left py-3">配置比例</th>
                <th className="text-left py-3">当前价值</th>
                <th className="text-left py-3">月定投</th>
                <th className="text-left py-3">收益率</th>
                <th className="text-left py-3">状态</th>
                <th className="text-left py-3">操作</th>
              </tr>
            </thead>
            <tbody>
              {investments.map((investment, index) => (
                <tr key={index} className="border-b">
                  <td className="py-3">
                    <div className="flex items-center space-x-2">
                      <span>{investment.type === 'stock' ? '📈' : '📊'}</span>
                      <span className="font-medium">{investment.name}</span>
                    </div>
                  </td>
                  <td className="py-3 text-gray-600">{investment.code}</td>
                  <td className="py-3">{investment.allocation}%</td>
                  <td className="py-3 font-bold">¥{investment.currentValue.toLocaleString()}</td>
                  <td className="py-3">¥{investment.monthlyInvest.toLocaleString()}</td>
                  <td className="py-3">
                    <span className={investment.performance > 0 ? 'text-green-600' : 'text-red-600'}>
                      {investment.performance > 0 ? '+' : ''}{investment.performance}%
                    </span>
                  </td>
                  <td className="py-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      investment.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {investment.status === 'active' ? '运行中' : '已暂停'}
                    </span>
                  </td>
                  <td className="py-3">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                      <button className="text-red-600 hover:text-red-800 text-sm">
                        {investment.status === 'active' ? '暂停' : '启用'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 智能建议 */}
      <div className="card mt-6">
        <h3 className="text-lg font-semibold mb-4">🤖 AI投资建议</h3>
        <div className="grid grid-2">
          <div className="space-y-3">
            <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
              <div className="font-medium text-green-800">📈 加仓机会</div>
              <div className="text-sm text-green-600 mt-1">
                沪深300 PE低于历史均值，建议本月定投金额增加50%
              </div>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <div className="font-medium text-blue-800">🔄 自动调整</div>
              <div className="text-sm text-blue-600 mt-1">
                系统将在下次定投时自动执行再平衡操作
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
              <div className="font-medium text-yellow-800">⚠️ 风险提醒</div>
              <div className="text-sm text-yellow-600 mt-1">
                近期市场波动加大，建议适当降低股票基金比例
              </div>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg border-l-4 border-purple-500">
              <div className="font-medium text-purple-800">💡 优化建议</div>
              <div className="text-sm text-purple-600 mt-1">
                考虑添加海外基金，提升组合分散度
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InvestmentCockpit
