import React, { useState } from 'react'

const AIAssistant = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'ai',
      content: '你好！我是你的AI理财助手 💎 小财。有什么理财问题可以随时问我！',
      time: '刚刚'
    }
  ])
  const [inputValue, setInputValue] = useState('')

  const quickActions = [
    { text: '分析我的支出', icon: '📊' },
    { text: '推荐投资产品', icon: '📈' },
    { text: '制定储蓄计划', icon: '💰' },
    { text: '风险评估', icon: '🛡️' }
  ]

  const aiResponses = {
    '分析我的支出': '根据您的消费数据，我发现外卖支出占比较高(28.5%)，建议：\n1. 每周自带午餐3天，可节省¥1,500\n2. 使用优惠券和会员折扣\n3. 设置月度预算上限¥3,000',
    '推荐投资产品': '基于您的风险偏好和目标，推荐以下配置：\n📈 易方达沪深300ETF (40%)\n📊 华夏债券基金 (30%)\n💰 货币基金 (30%)\n预期年化收益：8-10%',
    '制定储蓄计划': '为实现2年200万目标，建议：\n💰 月储蓄：¥65,000\n📈 投资年化收益：12%\n⚠️ 当前方案较激进，建议调整至3年或降低目标',
    '风险评估': '您的投资组合风险分析：\n🔴 股票基金占比75% (偏高)\n🟡 最大回撤-15.2%\n🟢 建议增加债券配置至30%降低波动'
  }

  const handleSendMessage = (content) => {
    if (!content.trim()) return

    // 添加用户消息
    const userMessage = {
      id: messages.length + 1,
      type: 'user',
      content: content,
      time: '刚刚'
    }

    // 生成AI回复
    const aiReply = {
      id: messages.length + 2,
      type: 'ai',
      content: aiResponses[content] || `我理解您想了解"${content}"。让我为您分析一下...\n\n基于您当前的财务状况，我建议您关注以下几个方面：\n1. 优化支出结构\n2. 提升投资收益\n3. 控制风险敞口\n\n需要我详细解释哪个方面吗？`,
      time: '刚刚'
    }

    setMessages(prev => [...prev, userMessage, aiReply])
    setInputValue('')
  }

  const handleQuickAction = (action) => {
    handleSendMessage(action.text)
  }

  return (
    <>
      {/* AI助手浮动按钮 */}
      <div 
        className="ai-assistant"
        onClick={() => setIsOpen(!isOpen)}
      >
        🤖
      </div>

      {/* AI助手对话窗口 */}
      {isOpen && (
        <div 
          className="fixed bottom-24 right-6 w-80 h-96 bg-white rounded-lg shadow-lg border"
          style={{ zIndex: 1000 }}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b bg-blue-600 text-white rounded-t-lg">
            <div className="flex items-center space-x-2">
              <span className="text-xl">🤖</span>
              <div>
                <div className="font-semibold">AI理财助手</div>
                <div className="text-xs opacity-90">小财 - 在线</div>
              </div>
            </div>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-200"
            >
              ✕
            </button>
          </div>

          {/* 消息区域 */}
          <div className="flex-1 p-4 h-64 overflow-y-auto">
            <div className="space-y-3">
              {messages.map(message => (
                <div 
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div 
                    className={`max-w-xs p-3 rounded-lg text-sm ${
                      message.type === 'user' 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    <div className="whitespace-pre-line">{message.content}</div>
                    <div className={`text-xs mt-1 ${
                      message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {message.time}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="p-3 border-t bg-gray-50">
            <div className="text-xs text-gray-600 mb-2">快捷操作：</div>
            <div className="grid grid-2 gap-2 mb-3">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickAction(action)}
                  className="p-2 bg-white border rounded text-xs hover:bg-gray-50 flex items-center space-x-1"
                >
                  <span>{action.icon}</span>
                  <span>{action.text}</span>
                </button>
              ))}
            </div>
          </div>

          {/* 输入区域 */}
          <div className="p-3 border-t">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
                placeholder="输入您的问题..."
                className="flex-1 p-2 border rounded text-sm"
              />
              <button
                onClick={() => handleSendMessage(inputValue)}
                className="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                发送
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 实时推送通知 */}
      {!isOpen && (
        <div className="fixed bottom-32 right-6 max-w-xs">
          <div className="bg-blue-600 text-white p-3 rounded-lg shadow-lg mb-2 animate-pulse">
            <div className="text-sm font-medium mb-1">💡 AI提醒</div>
            <div className="text-xs">
              检测到您的外卖支出本月已达¥3,200，建议控制在¥3,000以内
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default AIAssistant
