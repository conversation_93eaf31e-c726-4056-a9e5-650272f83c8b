<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💎 启航财富引擎 - 智能理财助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.5;
            font-weight: 400;
            
            /* 财富主题色彩 */
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f97316;
            
            /* 背景色 */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: #ffffff;
            
            /* 文字色 */
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-white: #ffffff;
            
            /* 边框色 */
            --border-color: #e2e8f0;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        body {
            margin: 0;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .card {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--text-white);
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .grid-4 {
            grid-template-columns: repeat(4, 1fr);
        }

        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        .nav {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
            cursor: pointer;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--primary-color);
        }

        .goal-progress {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            transition: width 0.3s ease;
            width: 21%;
        }

        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .text-success { color: var(--secondary-color); }
        .text-warning { color: var(--warning-color); }
        .text-primary { color: var(--primary-color); }
        .text-secondary { color: var(--text-secondary); }

        .ai-assistant {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: transform 0.2s;
            font-size: 1.5rem;
        }

        .ai-assistant:hover {
            transform: scale(1.1);
        }

        .hidden {
            display: none;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .alert-info {
            background-color: #eff6ff;
            border-color: #2563eb;
            color: #1e40af;
        }

        .alert-success {
            background-color: #f0fdf4;
            border-color: #10b981;
            color: #166534;
        }

        .alert-warning {
            background-color: #fefce8;
            border-color: #f59e0b;
            color: #92400e;
        }

        .alert-danger {
            background-color: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }

        .tabs {
            display: flex;
            background: #f3f4f6;
            padding: 0.25rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .tab {
            flex: 1;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            text-align: center;
            background: transparent;
            border: none;
            color: var(--text-secondary);
        }

        .tab.active {
            background: white;
            color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .space-x-2 > * + * {
            margin-left: 0.5rem;
        }

        .mb-2 {
            margin-bottom: 0.5rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .font-bold {
            font-weight: 700;
        }

        .font-semibold {
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="nav-brand">💎 启航财富引擎</a>
                <ul class="nav-menu">
                    <li><a href="#" class="nav-link active" onclick="showPage('dashboard')">📊 仪表板</a></li>
                    <li><a href="#" class="nav-link" onclick="showPage('goals')">🎯 目标设定</a></li>
                    <li><a href="#" class="nav-link" onclick="showPage('assets')">💰 资产管理</a></li>
                    <li><a href="#" class="nav-link" onclick="showPage('investment')">📈 投资驾驶舱</a></li>
                    <li><a href="#" class="nav-link" onclick="showPage('behavior')">🏆 行为激励</a></li>
                    <li><a href="#" class="nav-link" onclick="showPage('community')">👥 社群</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container">
        <!-- 仪表板页面 -->
        <div id="dashboard" class="page active">
            <!-- 目标进度卡片 -->
            <div class="goal-progress">
                <h2 class="text-2xl font-bold mb-4">🎯 2年200万目标进度</h2>
                <div class="grid grid-2">
                    <div>
                        <div class="text-2xl font-bold mb-2">¥420,000 / ¥2,000,000</div>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="text-lg">进度: 21.0% | 剩余时间: 19个月</div>
                    </div>
                    <div style="text-align: center;">
                        <div class="text-xl mb-2">当前状态</div>
                        <div class="text-2xl font-bold">🔥 需要加速</div>
                    </div>
                </div>
            </div>

            <!-- 核心指标 -->
            <div class="grid grid-4">
                <div class="card metric-card">
                    <div class="metric-value text-success">¥28,000</div>
                    <div class="metric-label">月收入</div>
                </div>
                <div class="card metric-card">
                    <div class="metric-value text-warning">¥16,800</div>
                    <div class="metric-label">月支出</div>
                </div>
                <div class="card metric-card">
                    <div class="metric-value text-primary">¥11,200</div>
                    <div class="metric-label">月储蓄</div>
                </div>
                <div class="card metric-card">
                    <div class="metric-value text-secondary">40%</div>
                    <div class="metric-label">储蓄率</div>
                </div>
            </div>

            <div class="grid grid-2">
                <!-- 资产分布 -->
                <div class="card">
                    <h3 class="text-xl font-semibold mb-4">💼 资产分布热力图</h3>
                    <div class="alert alert-info">
                        <strong>💰 现金及活期:</strong> ¥85,000 (20.2%)<br>
                        <strong>🏦 定期存款:</strong> ¥120,000 (28.6%)<br>
                        <strong>📈 股票基金:</strong> ¥150,000 (35.7%)<br>
                        <strong>📊 债券基金:</strong> ¥65,000 (15.5%)
                    </div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">
                        🔴 高风险 🟡 中风险 🟢 低风险
                    </div>
                </div>

                <!-- AI洞察 -->
                <div class="card">
                    <h3 class="text-xl font-semibold mb-4">🤖 AI智能洞察</h3>
                    <div class="alert alert-info">
                        <strong>💡 储蓄优化建议</strong><br>
                        检测到您的外卖支出本月达到4,780元，压缩空间约80%，可增加月储蓄3,800元
                    </div>
                    <div class="alert alert-success">
                        <strong>📈 投资机会</strong><br>
                        当前沪深300 PE为11.8，低于历史均值，建议增加股票基金定投金额
                    </div>
                    <div class="alert alert-warning">
                        <strong>⚠️ 风险提醒</strong><br>
                        您的基金组合夏普比率仅0.8，低于同类平均水平，建议优化配置
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-4">⚡ 快速操作</h3>
                <div class="grid grid-4">
                    <button class="btn btn-primary">💰 一键定投</button>
                    <button class="btn btn-secondary">📊 资产再平衡</button>
                    <button class="btn btn-primary">🎯 调整目标</button>
                    <button class="btn btn-secondary">📱 连接账户</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));
            
            // 显示选中的页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航链接状态
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>

    <!-- AI助手浮动按钮 -->
    <div class="ai-assistant" onclick="alert('AI助手：您好！我是您的理财助手小财，有什么可以帮您的吗？\n\n💡 建议：您的外卖支出偏高，建议本月控制在3000元以内')">
        🤖
    </div>
</body>
</html>
